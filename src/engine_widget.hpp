#ifndef ENGINEWIDGET_H
#define ENGINEWIDGET_H

#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_ringbuf.h"
#include "util/timer.h"
#include "dissector.h"
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/object_pool.h>
#include <yaBasicUtils/allocator.hpp>
#include <yaProtoRecord/precord.h>
#include <string>
#include <vector>
#include <deque>
#include <memory>
#include <string.h>
#include <assert.h>

class nxt_ProtoLayers
{
public:
    nxt_ProtoLayers()
    {
        protoLayerArray_.reserve(10);
    }

    void pushProtoLayer(pschema_t *schema)
    {
        protoLayerArray_.push_back(schema);
    }

    int toString(std::string &strLayers)
    {
        for (auto &schema : protoLayerArray_)
        {
            strLayers.append(pschema_get_proto_name(schema));
            strLayers.append(":");
        }

        strLayers.pop_back(); // 移除最后一个 ':';
        return 0;
    }

    void reset()
    {
        protoLayerArray_.clear();
    }

private:
    std::vector<pschema_t*>  protoLayerArray_;
};

// 意味着在各个 layer 之间以下数据可能发生变化;
class nxt_RegisterZone
{
public:
    friend struct nxt_Engine;

public:
    int storeMbuf(nxt_mbuf_t *mbuf)
    {
        mbuf_ = mbuf;
        return 0;
    }

    nxt_mbuf_t *loadMbuf(nxt_mbuf_t **ppmbuf)
    {
        if (mbuf_ != NULL && ppmbuf != NULL)
        {
            *ppmbuf = mbuf_;
        }

        return mbuf_;
    }

    int storeDissector(nxt_dissector_t *dissector)
    {
        dissector_ = dissector;
        return 0;
    }

    nxt_dissector_t *loadDissector(nxt_dissector_t **ppdissector)
    {
        if (dissector_ != NULL && ppdissector != NULL)
        {
            *ppdissector = dissector_;
        }

        return dissector_;
    }

    int storeSession(nxt_session_t *session)
    {
        session_ = session;
        return 0;
    }

    nxt_session_t *loadSession(nxt_session_t **ppsession)
    {
        if (session_ != NULL && ppsession != NULL)
        {
            *ppsession = session_;
        }

        return session_;
    }

    int storeDirection(nxt_direction_enum direction)
    {
        direction_ = direction;
        return 0;
    }

    nxt_direction_enum loadDirection()
    {
        return direction_;
    }

    void reset()
    {
        direction_     = NXT_DIR_UNKNOWN;
        dissector_     = NULL;
        mbuf_          = NULL;
        session_       = NULL;
    }

private:
    nxt_direction_enum   direction_;
    nxt_dissector_t *dissector_ = NULL;
    nxt_mbuf_t      *mbuf_      = NULL;
    nxt_session_t   *session_   = NULL;
};

class nxt_PacketZone
{
public:
    void init(nxt_mbuf_t *mbuf, precord_t *precord, time_t time)
    {
        mbuf_        = mbuf;
        precord_     = precord;

        pktTotalLen_ = nxt_mbuf_get_length(mbuf);
        captureTime_ = time;
        dissectTime_ = time;

        nxt_mbuf_range_t range = nxt_mbuf_range_tell(mbuf_);
        this->layerBegin_ = range.begin;
        this->layerEnd_   = range.end;
    }

    void reset()
    {
        protoLayers_.reset();
        t5Ipv4_       = {0, 0, 0, 0, 0};
        mbuf_         = NULL;
        pktTotalLen_  = 0;
        layerBegin_   = 0;
        layerEnd_     = 0;
        precord_      = NULL;
        captureTime_  = 0;
        carriedTime_  = 0;
        dissectTime_  = 0;
        protoFlags_   = 0;
        pktDirection_ = NXT_DIR_UNKNOWN;
    }

public:
    int storeDirection(nxt_direction_enum dir)
    {
        pktDirection_ = dir;
        return 0;
    }

    nxt_direction_enum loadDirection()
    {
        return pktDirection_;
    }

    int storeIpv4(uint8_t proto, uint32_t srcIp, uint32_t dstIp)
    {
        t5Ipv4_.proto   = proto;
        t5Ipv4_.srcAddr = srcIp;
        t5Ipv4_.dstAddr = dstIp;

        return 0;
    }

    int storePorts(uint16_t srcPort, uint16_t dstPort)
    {
        t5Ipv4_.srcPort = srcPort;
        t5Ipv4_.dstPort = dstPort;

        return 0;
    }

    int loadT5Ipv4(nxt_tuple_5_ipv4_t *t5)
    {
        memcpy(t5, &t5Ipv4_, sizeof *t5);
        return 0;
    }

    nxt_mbuf_t* loadMbuf()
    {
        return mbuf_;
    }

    int storePrecord(precord_t *precord)
    {
        precord_ = precord;
        return 0;
    }

    precord_t* loadPrecord()
    {
        return precord_;
    }

    precord_t* getPrecord()
    {
        return precord_;
    }

    int setLayerTotalLen(int layerLen)
    {
        layerEnd_ = layerBegin_ + layerLen;
        return 0;
    }

    int getTrailerBegin()
    {
        return layerEnd_;
    }

    int getTrailerEnd()
    {
        return pktTotalLen_;
    }

    int getTrailerLen()
    {
        return getTrailerEnd() - getTrailerBegin();
    }

    int getNextLayerBegin()
    {
        return layerBegin_;
    }

    int getNextLayerEnd()
    {
        return layerEnd_;
    }

    int getPktTotalLen()
    {
        return pktTotalLen_;
    }

    int getPktEffectiveLen()
    {
        return getPktTotalLen() - getTrailerLen();
    }

    time_t getCaptureTime()
    {
        return captureTime_;
    }

    time_t getDissectTime()
    {
        return dissectTime_;
    }

    int getProtoLayers(std::string &strLayers)
    {
        strLayers.clear();
        return protoLayers_.toString(strLayers);
    }

    nxt_direction_enum getDirection()
    {
        return pktDirection_;
    }

    int onDissectorDone(nxt_dissector_t *dissector, int consumeLen, nxt_mbuf_t *mbuf _U_)
    {
        protoLayers_.pushProtoLayer(dissector->getProtoSchema());

        // TODO: 重新计算 pktEnding, mbuf(副本) 的 length 可能被 dissector 通过 nxt_mbuf_update_length 调整过;
        // 等到切换为使用 mbuf 传递 layerLen 时才需要在此处进行更新，暂时依靠 setLayerTotalLen;
        // pktEnding_ = offset_ + layerLen_;

        layerBegin_ += consumeLen;
        return 0;
    }

private:
    nxt_ProtoLayers     protoLayers_;
    nxt_tuple_5_ipv4_t  t5Ipv4_;
    nxt_mbuf_t         *mbuf_         = NULL;
    uint16_t            pktTotalLen_  = 0;
    uint16_t            layerBegin_   = 0;    // 记录当前 layer 的 begin;
    uint16_t            layerEnd_     = 0;    // 记录当前 layer 的 end, 例如 eth/ipv4/udp/dns/trailer 中，会通过 ipv4 层宣告 layerEnd_ 在 dns 的最后一个字节;
    precord_t          *precord_      = NULL; // pktZone, 并不拥有 precord;
    time_t              captureTime_  = 0;
    time_t              carriedTime_  = 0;
    time_t              dissectTime_  = 0;
    uint32_t            protoFlags_   = 0;    // 记录是否 mpls, vlan, ipv6 等信息;
    nxt_direction_enum  pktDirection_ = NXT_DIR_UNKNOWN;
};

class nxt_EngineStats
{
public:
    friend struct nxt_Engine;

private:
    uint64_t packetCnt_    = 0;
    uint64_t recordCnt_    = 0;
    uint64_t badPacketCnt_ = 0;
};

class nxt_Defer
{
public:
    nxt_Defer(nxt_defer_callback_t callback, void *userdata)
        : callback_(callback)
        , userdata_(userdata)
    {
    }

    ~nxt_Defer()
    {
        callback_(userdata_);
    }

private:
    nxt_defer_callback_t callback_;
    void *               userdata_;
};

class nxt_ResourceLifetime
{
public:
    nxt_ResourceLifetime(ya_allocator_t *alloc)
        : alloc_(alloc)
    {
    }

public: // memory
    void *alloc(size_t size)
    {
        auto p = yv::make_alloc_ptr<void>(alloc_, size);
        memList_.push_back(std::move(p));
        return memList_.back().get();
    }

    void  defer(nxt_defer_callback_t callback, void *userdata)
    {
        assert(callback);
        if (NULL == callback)
        {
            // TODO: log
            return ;
        }

        deferList_.emplace_back(callback, userdata);
    }

private:
    using nxt_MemPtr = yv::AllocTrait<void>::unique_ptr;

private:
    ya_allocator_t                *alloc_;
    std::vector<nxt_MemPtr>        memList_;     // TODO: 今后需要调整为从预分配 arena 中分配，目前仅做 tracking;
    std::vector<nxt_Defer>         deferList_;   // WARNING: 这里的声明次序很关键，
                                                 // defer 有可能是对 mem 的 finish 操作(例如 session 中的 userdata)，
                                                 // 所以 defer_list 应该在 mem_list_ 之前销毁; defer_list_ 应该在 mem_list_ 之后声明;
};

template <typename Derived>
class nxt_AppendableBuf
{
public:
    uint32_t getWrittenBytes()
    {
        return hasWrittenBytes_;
    }

    int append(const uint8_t *from, uint32_t len)
    {
        auto writeBytes = static_cast<Derived *>(this)->doAppend(from, len);
        if (writeBytes < 0)
        {
            return -1;
        }

        hasWrittenBytes_ += writeBytes;
        return writeBytes;
    }

private:
    uint32_t hasWrittenBytes_ = 0;
};

class nxt_AppendableRawbuf : public nxt_AppendableBuf<nxt_AppendableRawbuf>
{
public:
    nxt_AppendableRawbuf(uint8_t *buff, uint32_t size)
        : buff_(buff)
        , buffSize_(size)
    {
    }

public:
    uint32_t getFreeSize()
    {
        return buffSize_ - getWrittenBytes();
    }

    int doAppend(const uint8_t *from, uint32_t len)
    {
        if (getFreeSize() < len)
        {
            return -1;
        }

        memcpy(buff_ + getWrittenBytes(), from, len);
        return len;
    }

private:
    uint8_t *buff_;
    uint32_t buffSize_;
};

class nxt_AppendableRingbuf : public nxt_AppendableBuf<nxt_AppendableRingbuf>
{
public:
    nxt_AppendableRingbuf(nxt_ringbuf_t *rbuf)
        : rbuf_(rbuf)
    {
    }

public:
    uint32_t getFreeSize()
    {
        return nxt_ringbuf_get_free_size(rbuf_);
    }

    int doAppend(const uint8_t *from, uint32_t len)
    {
        return nxt_ringbuf_push_back(rbuf_, from, len);
    }

private:
    nxt_ringbuf_t *rbuf_;
};

template <typename T, int size>
class nxt_RecentItemList
{
public:
    void push(const T& v)
    {
        if (recentList_.size() >= size)
        {
            recentList_.pop_front();
        }

        recentList_.push_back(std::forward<T>(v));
    }

    void push(T&& v)
    {
        if (recentList_.size() >= size)
        {
            recentList_.pop_front();
        }

        recentList_.push_back(std::forward<T>(v));
    }

    template <typename F>
    void foreach(F f)
    {
        for (const auto &v : recentList_)
        {
            f(v);
        }
    }

private:
    std::deque<T> recentList_;
};

struct nxt_AllocatorDeleter
{
    void operator()(ya_allocator_t *alloc)
    {
        ya_allocator_destroy(alloc);
    }
};

struct nxt_PlayerCreatorDeleter
{
    void operator()(player_creator_t *pc)
    {
        precord_layer_pool_destroy(pc);
    }
};

struct nxt_PrecordPoolDeleter
{
    void operator()(ya_object_pool_t *pool)
    {
        ya_object_pool_destroy(pool);
    }
};

struct nxt_PschemadbDeleter
{
    void operator()(pschema_db_t *db)
    {
        pschema_db_destroy(db);
    }
};

struct nxt_TimerSchedulerDeleter
{
    explicit nxt_TimerSchedulerDeleter(ya_allocator_t *alloc)
        : alloc_(alloc)
    {
    }

    void operator()(nxt_timer_scheduler_t *scheduler)
    {
        nxt_timer_scheduler_destroy(alloc_, scheduler);
    }

private:
    ya_allocator_t *alloc_;
};

#endif /* ENGINEWIDGET_H */
